cmake_minimum_required(VERSION 3.10)

project(DocxAutoFill CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)

# --- User Options ----
# Option to specify the path to the DuckX library
# Default paths assume Duck<PERSON> was cloned into ${PROJECT_SOURCE_DIR}/libs/duckx_src and built in its 'build' subdirectory
set(DEFAULT_DUCKX_SRC_DIR "${PROJECT_SOURCE_DIR}/libs/duckx_src")
set(DUCKX_INCLUDE_DIR "${DEFAULT_DUCKX_SRC_DIR}/include" CACHE PATH "Path to DuckX include directory (contains duckx.hpp)")
set(DUCKX_LIBRARY_DIR "${DEFAULT_DUCKX_SRC_DIR}/build" CACHE PATH "Path to DuckX build directory (contains the compiled library)")

# --- Find DuckX ----
# Add DuckX include directory
# Add DuckX include directory. This directory should contain duckx.hpp directly.
if(EXISTS "${DUCKX_INCLUDE_DIR}/duckx.hpp")
    include_directories(${DUCKX_INCLUDE_DIR})
    # 添加 pugixml 头文件路径
    include_directories("${DEFAULT_DUCKX_SRC_DIR}/thirdparty/pugixml")
    # 添加 zip 头文件路径
    include_directories("${DEFAULT_DUCKX_SRC_DIR}/thirdparty/zip")
    message(STATUS "DuckX include directory set to: ${DUCKX_INCLUDE_DIR}")
    message(STATUS "Added pugixml include directory: ${DEFAULT_DUCKX_SRC_DIR}/thirdparty/pugixml")
    message(STATUS "Added zip include directory: ${DEFAULT_DUCKX_SRC_DIR}/thirdparty/zip")
else()
    message(WARNING "DuckX header (duckx.hpp) not found in ${DUCKX_INCLUDE_DIR}. Please check DUCKX_INCLUDE_DIR.")
endif()

# Find DuckX library
# This assumes DuckX was built as a static library named 'duckx.lib' (Windows) or 'libduckx.a' (Linux/macOS)
if(WIN32)
    set(DUCKX_LIB_NAME duckx)
else()
    set(DUCKX_LIB_NAME duckx)
endif()

find_library(DUCKX_LIBRARY
    NAMES ${DUCKX_LIB_NAME}
    PATHS ${DUCKX_LIBRARY_DIR}
    # On Windows, CMake might look in subdirectories like Release/Debug if the build dir itself is given
    PATH_SUFFIXES Release Debug lib
)

if(DUCKX_LIBRARY)
    message(STATUS "Found DuckX library: ${DUCKX_LIBRARY}")
else()
    message(WARNING "DuckX library ('${DUCKX_LIB_NAME}') not found in ${DUCKX_LIBRARY_DIR} or its subdirectories (Release/Debug/lib). Please check DUCKX_LIBRARY_DIR and ensure DuckX is built.")
endif()

# --- Add Executable ----
add_executable(DocxAutoFill src/main.cpp)

# --- Link Libraries ----
# Link DuckX to the executable
if(DUCKX_LIBRARY)
    target_link_libraries(DocxAutoFill PRIVATE ${DUCKX_LIBRARY})
    message(STATUS "Linking DocxAutoFill with DuckX library: ${DUCKX_LIBRARY}")
else()
    message(WARNING "DuckX library not found. DocxAutoFill will not be linked with DuckX.")
endif()

# --- Installation (Optional) ----
# install(TARGETS DocxAutoFill DESTINATION bin)

# --- Message ----
message(STATUS "CMake configuration for DocxAutoFill finished.")
message(STATUS "To build, run your build tool (e.g., make, ninja, or open in Visual Studio).")
message(STATUS "Ensure DuckX library is correctly built and its path (DUCKX_INCLUDE_DIR, DUCKX_LIBRARY_DIR) is set if not in default locations.")