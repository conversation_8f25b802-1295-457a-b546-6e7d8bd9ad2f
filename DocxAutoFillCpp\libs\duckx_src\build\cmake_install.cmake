# Install script for directory: C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files (x86)/duckx")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set path to fallback-tool for dependency-resolution.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "E:/mingw64/bin/objdump.exe")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY FILES "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/build/libduckx.a")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/duckx/duckxConfig.cmake")
    file(DIFFERENT _cmake_export_file_changed FILES
         "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/duckx/duckxConfig.cmake"
         "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/build/CMakeFiles/Export/7a4a6fdb5ecce73846bd34d18f059b71/duckxConfig.cmake")
    if(_cmake_export_file_changed)
      file(GLOB _cmake_old_config_files "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/duckx/duckxConfig-*.cmake")
      if(_cmake_old_config_files)
        string(REPLACE ";" ", " _cmake_old_config_files_text "${_cmake_old_config_files}")
        message(STATUS "Old export file \"$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/duckx/duckxConfig.cmake\" will be replaced.  Removing files [${_cmake_old_config_files_text}].")
        unset(_cmake_old_config_files_text)
        file(REMOVE ${_cmake_old_config_files})
      endif()
      unset(_cmake_old_config_files)
    endif()
    unset(_cmake_export_file_changed)
  endif()
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/duckx" TYPE FILE FILES "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/build/CMakeFiles/Export/7a4a6fdb5ecce73846bd34d18f059b71/duckxConfig.cmake")
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^()$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/duckx" TYPE FILE FILES "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/build/CMakeFiles/Export/7a4a6fdb5ecce73846bd34d18f059b71/duckxConfig-noconfig.cmake")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/duckx" TYPE FILE FILES
    "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/include/duckx.hpp"
    "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/include/constants.hpp"
    "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/include/duckxiterator.hpp"
    "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/thirdparty/pugixml/pugixml.hpp"
    "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/thirdparty/pugixml/pugiconfig.hpp"
    "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/thirdparty/zip/zip.h"
    )
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/build/test/cmake_install.cmake")

endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
if(CMAKE_INSTALL_LOCAL_ONLY)
  file(WRITE "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/build/install_local_manifest.txt"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
endif()
if(CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_COMPONENT MATCHES "^[a-zA-Z0-9_.+-]+$")
    set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INSTALL_COMPONENT}.txt")
  else()
    string(MD5 CMAKE_INST_COMP_HASH "${CMAKE_INSTALL_COMPONENT}")
    set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INST_COMP_HASH}.txt")
    unset(CMAKE_INST_COMP_HASH)
  endif()
else()
  set(CMAKE_INSTALL_MANIFEST "install_manifest.txt")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  file(WRITE "C:/Users/<USER>/Desktop/计算机相关文件/6.报告自动化识别出具软件/DocxAutoFillCpp/libs/duckx_src/build/${CMAKE_INSTALL_MANIFEST}"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
endif()
