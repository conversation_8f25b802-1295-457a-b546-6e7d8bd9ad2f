/*
 * rtd.css
 * ~~~~~~~~~~~~~~~
 *
 * Sphinx stylesheet -- sphinxdoc theme.  Originally created by
 * <PERSON><PERSON> for Werkzeug.
 *
 * Customized for ReadTheDocs by <PERSON> & <PERSON>
 *
 * :copyright: Copyright 2007-2010 by the Sphinx team, see AUTHORS.
 * :license: BSD, see LICENSE for details.
 *
 */

/* RTD colors
 * light blue: {{ theme_light_color }}
 * medium blue: {{ theme_medium_color }}
 * dark blue: {{ theme_dark_color }}
 * dark grey: {{ theme_grey_color }}
 *
 * medium blue hover: {{ theme_medium_color_hover }};
 * green highlight: {{ theme_green_highlight }}
 * light blue (project bar): {{ theme_light_color }}
 */

@import url("basic.css");

/* PAGE LAYOUT -------------------------------------------------------------- */

body {
    font: 100%/1.5 "ff-meta-web-pro-1","ff-meta-web-pro-2",<PERSON><PERSON>,"Helvetica Neue",sans-serif;
    text-align: center;
    color: black;
    background-color: {{ theme_background }};
    padding: 0;
    margin: 0;
}

div.document {
    text-align: left;
    background-color: {{ theme_light_color }};
}

div.bodywrapper {
    background-color: {{ theme_white }};
    border-left: 1px solid {{ theme_lighter_gray }};
    border-bottom: 1px solid {{ theme_lighter_gray }};
    margin: 0 0 0 16em;
}

div.body {
    margin: 0;
    padding: 0.5em 1.3em;
    max-width: 55em;
    min-width: 20em;
}

div.related {
    font-size: 1em;
    background-color: {{ theme_background }};
}

div.documentwrapper {
    float: left;
    width: 100%;
    background-color: {{ theme_light_color }};
}


/* HEADINGS --------------------------------------------------------------- */

h1 {
    margin: 0;
    padding: 0.7em 0 0.3em 0;
    font-size: 1.5em;
    line-height: 1.15;
    color: {{ theme_h1 }};
    clear: both;
}

h2 {
    margin: 2em 0 0.2em 0;
    font-size: 1.35em;
    padding: 0;
    color: {{ theme_h2 }};
}

h3 {
    margin: 1em 0 -0.3em 0;
    font-size: 1.2em;
    color: {{ theme_h3 }};
}

div.body h1 a, div.body h2 a, div.body h3 a, div.body h4 a, div.body h5 a, div.body h6 a {
    color: black;
}

h1 a.anchor, h2 a.anchor, h3 a.anchor, h4 a.anchor, h5 a.anchor, h6 a.anchor {
    display: none;
    margin: 0 0 0 0.3em;
    padding: 0 0.2em 0 0.2em;
    color: {{ theme_gray_a }} !important;
}

h1:hover a.anchor, h2:hover a.anchor, h3:hover a.anchor, h4:hover a.anchor,
h5:hover a.anchor, h6:hover a.anchor {
    display: inline;
}

h1 a.anchor:hover, h2 a.anchor:hover, h3 a.anchor:hover, h4 a.anchor:hover,
h5 a.anchor:hover, h6 a.anchor:hover {
    color: {{ theme_gray_7 }};
    background-color: {{ theme_dirty_white }};
}


/* LINKS ------------------------------------------------------------------ */

/* Normal links get a pseudo-underline */
a {
    color: {{ theme_link_color }};
    text-decoration: none;
    border-bottom: 1px solid {{ theme_link_color_decoration }};
}

/* Links in sidebar, TOC, index trees and tables have no underline */
.sphinxsidebar a,
.toctree-wrapper a,
.indextable a,
#indices-and-tables a {
    color: {{ theme_dark_gray }};
    text-decoration: none;
    border-bottom: none;
}

/* Most links get an underline-effect when hovered */
a:hover,
div.toctree-wrapper a:hover,
.indextable a:hover,
#indices-and-tables a:hover {
    color: {{ theme_black }};
    text-decoration: none;
    border-bottom: 1px solid {{ theme_black }};
}

/* Footer links */
div.footer a {
    color: {{ theme_background_text_link }};
    text-decoration: none;
    border: none;
}
div.footer a:hover {
    color: {{ theme_medium_color_link_hover }};
    text-decoration: underline;
    border: none;
}

/* Permalink anchor (subtle grey with a red hover) */
div.body a.headerlink {
    color: {{ theme_lighter_gray }};
    font-size: 1em;
    margin-left: 6px;
    padding: 0 4px 0 4px;
    text-decoration: none;
    border: none;
}
div.body a.headerlink:hover {
    color: {{ theme_negative_text }};
    border: none;
}


/* NAVIGATION BAR --------------------------------------------------------- */

div.related ul {
    height: 2.5em;
}

div.related ul li {
    margin: 0;
    padding: 0.65em 0;
    float: left;
    display: block;
    color: {{ theme_background_link_half }}; /* For the >> separators */
    font-size: 0.8em;
}

div.related ul li.right {
    float: right;
    margin-right: 5px;
    color: transparent; /* Hide the | separators */
}

/* "Breadcrumb" links in nav bar */
div.related ul li a {
    order: none;
    background-color: inherit;
    font-weight: bold;
    margin: 6px 0 6px 4px;
    line-height: 1.75em;
    color: {{ theme_background_link }};
    text-shadow: 0 1px rgba(0, 0, 0, 0.5);
    padding: 0.4em 0.8em;
    border: none;
    border-radius: 3px;
}
/* previous / next / modules / index links look more like buttons */
div.related ul li.right a {
    margin: 0.375em 0;
    background-color: {{ theme_medium_color_hover }};
    text-shadow: 0 1px rgba(0, 0, 0, 0.5);
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
}
/* All navbar links light up as buttons when hovered */
div.related ul li a:hover {
    background-color: {{ theme_medium_color }};
    color: {{ theme_white }};
    text-decoration: none;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
}
/* Take extra precautions for tt within links */
a tt,
div.related ul li a tt {
    background: inherit !important;
    color: inherit !important;
}


/* SIDEBAR ---------------------------------------------------------------- */

div.sphinxsidebarwrapper {
    padding: 0;
}

div.sphinxsidebar {
    margin: 0;
    margin-left: -100%;
    float: left;
    top: 3em;
    left: 0;
    padding: 0 1em;
    width: 14em;
    font-size: 1em;
    text-align: left;
    background-color: {{ theme_light_color }};
}

div.sphinxsidebar img {
    max-width: 12em;
}

div.sphinxsidebar h3, div.sphinxsidebar h4 {
    margin: 1.2em 0 0.3em 0;
    font-size: 1em;
    padding: 0;
    color: {{ theme_gray_2 }};
    font-family: "ff-meta-web-pro-1", "ff-meta-web-pro-2", "Arial", "Helvetica Neue", sans-serif;
}

div.sphinxsidebar h3 a {
    color: {{ theme_grey_color }};
}

div.sphinxsidebar ul,
div.sphinxsidebar p {
    margin-top: 0;
    padding-left: 0;
    line-height: 130%;
    background-color: {{ theme_light_color }};
}

/* No bullets for nested lists, but a little extra indentation */
div.sphinxsidebar ul ul {
    list-style-type: none;
    margin-left: 1.5em;
    padding: 0;
}

/* A little top/bottom padding to prevent adjacent links' borders
 * from overlapping each other */
div.sphinxsidebar ul li {
    padding: 1px 0;
}

/* A little left-padding to make these align with the ULs */
div.sphinxsidebar p.topless {
    padding-left: 0 0 0 1em;
}

/* Make these into hidden one-liners */
div.sphinxsidebar ul li,
div.sphinxsidebar p.topless {
    white-space: nowrap;
    overflow: hidden;
}
/* ...which become visible when hovered */
div.sphinxsidebar ul li:hover,
div.sphinxsidebar p.topless:hover {
    overflow: visible;
}

/* Search text box and "Go" button */
#searchbox {
    margin-top: 2em;
    margin-bottom: 1em;
    background: {{ theme_dirtier_white }};
    padding: 0.5em;
    border-radius: 6px;
    -moz-border-radius: 6px;
    -webkit-border-radius: 6px;
}
#searchbox h3 {
    margin-top: 0;
}

/* Make search box and button abut and have a border */
input,
div.sphinxsidebar input {
    border: 1px solid {{ theme_gray_9 }};
    float: left;
}

/* Search textbox */
input[type="text"] {
    margin: 0;
    padding: 0 3px;
    height: 20px;
    width: 144px;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    -moz-border-radius-topleft: 3px;
    -moz-border-radius-bottomleft: 3px;
    -webkit-border-top-left-radius: 3px;
    -webkit-border-bottom-left-radius: 3px;
}
/* Search button */
input[type="submit"] {
    margin: 0 0 0 -1px; /* -1px prevents a double-border with textbox */
    height: 22px;
    color: {{ theme_dark_gray }};
    background-color: {{ theme_light_color }};
    padding: 1px 4px;
    font-weight: bold;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    -moz-border-radius-topright: 3px;
    -moz-border-radius-bottomright: 3px;
    -webkit-border-top-right-radius: 3px;
    -webkit-border-bottom-right-radius: 3px;
}
input[type="submit"]:hover {
    color: {{ theme_white }};
    background-color: {{ theme_green_highlight }};
}

div.sphinxsidebar p.searchtip {
    clear: both;
    padding: 0.5em 0 0 0;
    background: {{ theme_dirtier_white }};
    color: {{ theme_gray }};
    font-size: 0.9em;
}

/* Sidebar links are unusual */
div.sphinxsidebar li a,
div.sphinxsidebar p a {
    background: {{ theme_light_color }}; /* In case links overlap main content */
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border: 1px solid transparent; /* To prevent things jumping around on hover */
    padding: 0 5px 0 5px;
}
div.sphinxsidebar li a:hover,
div.sphinxsidebar p a:hover {
    color: {{ theme_black }};
    text-decoration: none;
    border: 1px solid {{ theme_light_gray }};
}

/* Tweak any link appearing in a heading */
div.sphinxsidebar h3 a {
}




/* OTHER STUFF ------------------------------------------------------------ */

cite, code, tt {
    font-family: 'Consolas', 'Deja Vu Sans Mono',
                 'Bitstream Vera Sans Mono', monospace;
    font-size: 0.95em;
    letter-spacing: 0.01em;
}

tt {
    background-color: {{ theme_code_background }};
    color: {{ theme_dark_gray }};
}

tt.descname, tt.descclassname, tt.xref {
    border: 0;
}

hr {
    border: 1px solid {{ theme_ruler }};
    margin: 2em;
}

pre, #_fontwidthtest {
    font-family: 'Consolas', 'Deja Vu Sans Mono',
                 'Bitstream Vera Sans Mono', monospace;
    margin: 1em 2em;
    font-size: 0.95em;
    letter-spacing: 0.015em;
    line-height: 120%;
    padding: 0.5em;
    border: 1px solid {{ theme_lighter_gray }};
    background-color: {{ theme_code_background }};
    border-radius: 6px;
    -moz-border-radius: 6px;
    -webkit-border-radius: 6px;
}

pre a {
    color: inherit;
    text-decoration: underline;
}

td.linenos pre {
    padding: 0.5em 0;
}

div.quotebar {
    background-color: {{ theme_almost_white }};
    max-width: 250px;
    float: right;
    padding: 2px 7px;
    border: 1px solid {{ theme_lighter_gray }};
}

div.topic {
    background-color: {{ theme_almost_white }};
}

table {
    border-collapse: collapse;
    margin: 0 -0.5em 0 -0.5em;
}

table td, table th {
    padding: 0.2em 0.5em 0.2em 0.5em;
}


/* ADMONITIONS AND WARNINGS ------------------------------------------------- */

/* Shared by admonitions, warnings and sidebars */
div.admonition,
div.warning,
div.sidebar {
    font-size: 0.9em;
    margin: 2em;
    padding: 0;
    /*
    border-radius: 6px;
    -moz-border-radius: 6px;
    -webkit-border-radius: 6px;
    */
}
div.admonition p,
div.warning p,
div.sidebar p {
    margin: 0.5em 1em 0.5em 1em;
    padding: 0;
}
div.admonition pre,
div.warning pre,
div.sidebar pre {
    margin: 0.4em 1em 0.4em 1em;
}
div.admonition p.admonition-title,
div.warning p.admonition-title,
div.sidebar p.sidebar-title {
    margin: 0;
    padding: 0.1em 0 0.1em 0.5em;
    color: white;
    font-weight: bold;
    font-size: 1.1em;
    text-shadow: 0 1px rgba(0, 0, 0, 0.5);
}
div.admonition ul, div.admonition ol,
div.warning ul, div.warning ol,
div.sidebar ul, div.sidebar ol {
    margin: 0.1em 0.5em 0.5em 3em;
    padding: 0;
}


/* Admonitions and sidebars only */
div.admonition, div.sidebar {
    border: 1px solid {{ theme_positive_dark }};
    background-color: {{ theme_positive_light }};
}
div.admonition p.admonition-title,
div.sidebar p.sidebar-title {
    background-color: {{ theme_positive_medium }};
    border-bottom: 1px solid {{ theme_positive_dark }};
}


/* Warnings only */
div.warning {
    border: 1px solid {{ theme_negative_dark }};
    background-color: {{ theme_negative_light }};
}
div.warning p.admonition-title {
    background-color: {{ theme_negative_medium }};
    border-bottom: 1px solid {{ theme_negative_dark }};
}


/* Sidebars only */
div.sidebar {
  max-width: 200px;
}



div.versioninfo {
    margin: 1em 0 0 0;
    border: 1px solid {{ theme_lighter_gray }};
    background-color: {{ theme_light_medium_color }};
    padding: 8px;
    line-height: 1.3em;
    font-size: 0.9em;
}

.viewcode-back {
    font-family: 'Lucida Grande', 'Lucida Sans Unicode', 'Geneva',
                 'Verdana', sans-serif;
}

div.viewcode-block:target {
    background-color: {{ theme_viewcode_bg }};
    border-top: 1px solid {{ theme_viewcode_border }};
    border-bottom: 1px solid {{ theme_viewcode_border }};
}

dl {
    margin: 1em 0 2.5em 0;
}

/* Highlight target when you click an internal link */
dt:target {
    background: {{ theme_highlight }};
}
/* Don't highlight whole divs */
div.highlight {
    background: transparent;
}
/* But do highlight spans (so search results can be highlighted) */
span.highlight {
    background: {{ theme_highlight }};
}

div.footer {
    background-color: {{ theme_background }};
    color: {{ theme_background_text }};
    padding: 0 2em 2em 2em;
    clear: both;
    font-size: 0.8em;
    text-align: center;
}

p {
    margin: 0.8em 0 0.5em 0;
}

.section p img {
    margin: 1em 2em;
}


/* MOBILE LAYOUT -------------------------------------------------------------- */

@media screen and (max-width: 600px) {

    h1, h2, h3, h4, h5 {
        position: relative;
    }

    ul {
        padding-left: 1.75em;
    }

    div.bodywrapper a.headerlink, #indices-and-tables h1 a {
        color: {{ theme_almost_dirty_white }};
        font-size: 80%;
        float: right;
        line-height: 1.8;
        position: absolute;
        right: -0.7em;
        visibility: inherit;
    }

    div.bodywrapper h1 a.headerlink, #indices-and-tables h1 a {
        line-height: 1.5;
    }

    pre {
        font-size: 0.7em;
        overflow: auto;
        word-wrap: break-word;
        white-space: pre-wrap;
    }

    div.related ul {
        height: 2.5em;
        padding: 0;
        text-align: left;
    }

    div.related ul li {
        clear: both;
        color: {{ theme_dark_color }};
        padding: 0.2em 0;
    }

    div.related ul li:last-child {
        border-bottom: 1px dotted {{ theme_medium_color }};
        padding-bottom: 0.4em;
        margin-bottom: 1em;
        width: 100%;
    }

    div.related ul li a {
        color: {{ theme_dark_color }};
        padding-right: 0;
    }

    div.related ul li a:hover {
        background: inherit;
        color: inherit;
    }

    div.related ul li.right {
        clear: none;
        padding: 0.65em 0;
        margin-bottom: 0.5em;
    }

    div.related ul li.right a {
        color: {{ theme_white }};
        padding-right: 0.8em;
    }

    div.related ul li.right a:hover {
        background-color: {{ theme_medium_color }};
    }

    div.body {
        clear: both;
        min-width: 0;
        word-wrap: break-word;
    }

    div.bodywrapper {
        margin: 0 0 0 0;
    }

    div.sphinxsidebar {
        float: none;
        margin: 0;
        width: auto;
    }

    div.sphinxsidebar input[type="text"] {
        height: 2em;
        line-height: 2em;
        width: 70%;
    }

    div.sphinxsidebar input[type="submit"] {
        height: 2em;
        margin-left: 0.5em;
        width: 20%;
    }

    div.sphinxsidebar p.searchtip {
        background: inherit;
        margin-bottom: 1em;
    }

    div.sphinxsidebar ul li, div.sphinxsidebar p.topless {
        white-space: normal;
    }

    .bodywrapper img {
        display: block;
        margin-left: auto;
        margin-right: auto;
        max-width: 100%;
    }

    div.documentwrapper {
        float: none;
    }

    div.admonition, div.warning, pre, blockquote {
        margin-left: 0em;
        margin-right: 0em;
    }

    .body p img {
        margin: 0;
    }

    #searchbox {
        background: transparent;
    }

    .related:not(:first-child) li {
        display: none;
    }

    .related:not(:first-child) li.right {
        display: block;
    }

    div.footer {
        padding: 1em;
    }

    .rtd_doc_footer .badge {
        float: none;
        margin: 1em auto;
        position: static;
    }

    .rtd_doc_footer .badge.revsys-inline {
        margin-right: auto;
        margin-bottom: 2em;
    }

    table.indextable {
        display: block;
        width: auto;
    }

    .indextable tr {
        display: block;
    }

    .indextable td {
        display: block;
        padding: 0;
        width: auto !important;
    }

    .indextable td dt {
        margin: 1em 0;
    }

    ul.search {
        margin-left: 0.25em;
    }

    ul.search li div.context {
        font-size: 90%;
        line-height: 1.1;
        margin-bottom: 1;
        margin-left: 0;
    }

}
