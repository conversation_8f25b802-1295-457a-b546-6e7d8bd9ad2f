#include "../libs/duckx_src/include/duckx.hpp"
#include <iostream>
#include <string>
#include <regex>
#include <map>
#include <vector>
#include <fstream>

/**
 * @brief 占位符结构体，存储占位符的相关信息
 */
struct Placeholder {
    std::string name;        // 占位符名称
    std::string full_match;  // 完整的占位符文本，如 {{placeholder_name}}
    int paragraph_index;     // 所在段落索引
    int run_index;           // 所在文本运行索引
};

/**
 * @brief 使用正则表达式识别文本中的占位符
 * 
 * @param text 要检查的文本
 * @return std::vector<std::string> 识别到的占位符列表
 */
std::vector<std::string> identifyPlaceholders(const std::string& text) {
    std::vector<std::string> placeholders;
    std::regex placeholder_pattern(R"(\{\{([^\}]+)\}\})"); // 匹配 {{placeholder_name}} 格式
    
    std::sregex_iterator it(text.begin(), text.end(), placeholder_pattern);
    std::sregex_iterator end;
    
    while (it != end) {
        std::smatch match = *it;
        placeholders.push_back(match.str()); // 添加完整匹配
        ++it;
    }
    
    return placeholders;
}

/**
 * @brief 从占位符文本中提取占位符名称
 * 
 * @param placeholder 完整的占位符文本，如 {{placeholder_name}}
 * @return std::string 提取的占位符名称，如 placeholder_name
 */
std::string extractPlaceholderName(const std::string& placeholder) {
    std::regex name_pattern(R"(\{\{([^\}]+)\}\})");
    std::smatch match;
    
    if (std::regex_search(placeholder, match, name_pattern) && match.size() > 1) {
        return match.str(1); // 返回第一个捕获组，即占位符名称
    }
    
    return "";
}

/**
 * @brief 加载替换数据，从文件或其他数据源
 * 
 * @param data_path 数据文件路径
 * @return std::map<std::string, std::string> 占位符名称到替换值的映射
 */
std::map<std::string, std::string> loadReplacementData(const std::string& data_path) {
    // 这里简化处理，实际项目中可能需要从CSV、JSON或数据库加载
    std::map<std::string, std::string> data;
    
    // 示例数据 - 在实际应用中，这里应该从文件或数据库加载
    data["bridge_name"] = "盘兴高速下平川特大桥";
    data["report_date"] = "2024年第一季度";
    data["inspection_date"] = "2024年3月15日";
    data["temperature"] = "23.5℃";
    data["humidity"] = "65%";
    data["wind_speed"] = "3.2m/s";
    data["max_displacement"] = "12.3mm";
    data["conclusion"] = "桥梁结构状态良好，未发现明显异常。";
    
    return data;
}

/**
 * @brief 主函数，程序入口点。
 *
 * @param argc 命令行参数数量。
 * @param argv 命令行参数数组。
 * @return int 程序退出代码。
 */
int main(int argc, char* argv[]) {
    // 打印欢迎信息
    std::cout << "DocxAutoFill: 报告自动化识别填空并出具软件" << std::endl;

    // 检查命令行参数
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <docx文件路径> [输出文件路径]" << std::endl;
        std::cerr << "请提供.docx文件的完整路径作为参数。" << std::endl;
        return 1;
    }

    // 获取输入的docx文件路径
    const char* docx_file_path = argv[1];
    std::cout << "输入文档路径: " << docx_file_path << std::endl;
    
    // 确定输出文件路径
    std::string output_path;
    if (argc >= 3) {
        output_path = argv[2];
    } else {
        // 默认输出路径：在原文件名基础上添加_filled后缀
        std::string input_path(docx_file_path);
        size_t dot_pos = input_path.find_last_of('.');
        if (dot_pos != std::string::npos) {
            output_path = input_path.substr(0, dot_pos) + "_filled" + input_path.substr(dot_pos);
        } else {
            output_path = input_path + "_filled.docx";
        }
    }
    std::cout << "输出文档路径: " << output_path << std::endl;

    // 打开文档
    std::cout << "正在打开文档..." << std::endl;
    
    // 检查文件是否存在
    std::ifstream file_check(docx_file_path);
    if (!file_check.good()) {
        std::cerr << "错误: 文件 '" << docx_file_path << "' 不存在或无法访问。" << std::endl;
        return 1;
    }
    file_check.close();
    
    duckx::Document doc(docx_file_path);
    try {
        if (!doc.open()) {
            std::cerr << "错误: 无法打开文档 '" << docx_file_path << "'." << std::endl;
            std::cerr << "请确保文件是有效的.docx文件，且程序有读取权限。" << std::endl;
            return 1;
        }
        std::cout << "文档打开成功。" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "错误: 打开文档时发生异常: " << e.what() << std::endl;
        return 1;
    }

    // 加载替换数据
    std::map<std::string, std::string> replacement_data = loadReplacementData("");
    std::cout << "已加载 " << replacement_data.size() << " 个数据项用于替换。" << std::endl;

    // 遍历文档，识别并替换占位符
    std::cout << "\n--- 开始处理文档 ---" << std::endl;
    int paragraph_index = 0;
    int placeholders_found = 0;
    int placeholders_replaced = 0;
    
    for (auto p : doc.paragraphs()) {
        int run_index = 0;
        for (auto r : p.runs()) {
            std::string text = r.get_text();
            std::vector<std::string> placeholders = identifyPlaceholders(text);
            
            if (!placeholders.empty()) {
                placeholders_found += placeholders.size();
                std::cout << "在段落 " << paragraph_index << ", 文本运行 " << run_index 
                          << " 中发现 " << placeholders.size() << " 个占位符" << std::endl;
                
                // 替换占位符
                for (const auto& placeholder : placeholders) {
                    std::string name = extractPlaceholderName(placeholder);
                    if (!name.empty() && replacement_data.find(name) != replacement_data.end()) {
                        // 替换文本中的占位符
                        std::string new_text = text;
                        size_t pos = new_text.find(placeholder);
                        if (pos != std::string::npos) {
                            new_text.replace(pos, placeholder.length(), replacement_data[name]);
                            r.set_text(new_text);
                            text = new_text; // 更新当前文本，以便处理同一运行中的多个占位符
                            placeholders_replaced++;
                            
                            std::cout << "  替换: '" << placeholder << "' -> '" << replacement_data[name] << "'" << std::endl;
                        }
                    } else {
                        std::cout << "  警告: 未找到占位符 '" << name << "' 的替换数据" << std::endl;
                    }
                }
            }
            run_index++;
        }
        paragraph_index++;
    }
    std::cout << "--- 文档处理完成 ---" << std::endl;
    std::cout << "共发现 " << placeholders_found << " 个占位符，成功替换 " << placeholders_replaced << " 个。" << std::endl;

    // 保存文档
    std::cout << "正在保存文档到 '" << output_path << "'..." << std::endl;
    if (doc.save(output_path.c_str())) {
        std::cout << "文档保存成功。" << std::endl;
    } else {
        std::cerr << "错误: 无法保存文档。" << std::endl;
        return 1;
    }

    return 0;
}