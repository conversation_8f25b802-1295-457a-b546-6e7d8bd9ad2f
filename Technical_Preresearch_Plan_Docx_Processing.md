# 技术预研计划：C++ 处理 .docx 文件（基于DuckX）

## 1. 背景与目标

根据项目需求文档（`Project_Requirements_Report_Automation.md`），本项目核心技术挑战之一是使用C++有效处理 `.docx` 文件，特别是实现占位符的识别、内容的填充，并最大限度地保留原始格式。初步调研已识别出 DuckX 作为一个潜在的开源C++库。 <mcreference link="https://github.com/amiremohamadi/DuckX" index="3">3</mcreference>

本预研计划旨在通过原型开发，验证DuckX库在满足以下关键需求方面的能力：

-   读取和解析 `.docx` 文件结构。
-   准确识别预定义的占位符。
-   替换占位符文本。
-   在文本替换过程中保留原有的字符级和段落级格式。
-   评估其易用性、稳定性和局限性。

## 2. 预研范围与方法

### 2.1. 环境搭建
-   **操作系统**：Windows
-   **编译器**：MSVC (Visual Studio)
-   **构建系统**：CMake
-   **依赖库**：
    -   DuckX: 获取最新版本并编译集成。
    -   (可能需要的其他辅助库，如XML解析器，如果DuckX内部未完全封装)
-   **IDE**：Visual Studio

### 2.2. 测试用例设计
-   **输入文档**：使用用户提供的示例文档 `盘兴高速下平川特大桥健康监测运维报告（2024年第一季度）.docx` 作为主要测试对象。
-   **占位符定义**：
    -   初步采用简单文本标记，例如 `{{placeholder_name}}`。
    -   在示例文档中手动插入几个此类占位符，覆盖不同场景：
        -   简单段落中的占位符。
        -   表格单元格内的占位符。
        -   具有特定格式（如加粗、斜体、颜色）的占位符文本。
-   **预期输出**：
    -   能够成功读取文档内容。
    -   能够定位并提取所有占位符及其名称。
    -   能够用新的文本替换占位符。
    -   替换后的文本应继承占位符原有的格式。
    -   文档的其余部分结构和格式不受影响。

### 2.3. 预研步骤

1.  **DuckX库的获取、编译与集成**：
    -   **获取DuckX源代码**：从GitHub克隆DuckX仓库。 <mcreference link="https://github.com/amiremohamadi/DuckX" index="5">5</mcreference>
        ```bash
        git clone https://github.com/amiremohamadi/DuckX.git
        cd DuckX
        ```
    -   **编译DuckX**：使用CMake生成项目文件并编译。建议在DuckX目录外创建一个构建目录，或者在DuckX内创建一个`build`目录。 <mcreference link="https://github.com/amiremohamadi/DuckX" index="5">5</mcreference>
        ```bash
        mkdir build
        cd build
        cmake .. 
        # 对于Visual Studio, 之后可以用IDE打开生成的.sln文件编译，或者继续使用cmake:
        cmake --build . --config Release # 或者 Debug
        ```
        编译后，会在 `build` 目录（或指定的安装目录）下找到库文件（如 `duckx.lib` 或 `libduckx.a`）和头文件。
    -   **集成到本项目**：
        -   将DuckX的头文件目录（通常是 `DuckX/duckx` 或 `DuckX/include`）和生成的库文件目录配置到本项目的 `CMakeLists.txt` 中（通过 `DUCKX_INCLUDE_DIR` 和 `DUCKX_LIBRARY_DIR` 变量）。
        -   确保 `CMakeLists.txt` 正确链接DuckX库到 `DocxAutoFill` 可执行文件。

2.  **基本文档读取与遍历**：
    -   使用DuckX API打开示例文档。
    -   遍历文档中的段落（paragraphs）和文本运行（runs）。
    -   尝试输出提取到的文本内容，验证读取功能是否正常。
    ```cpp
    // 示例代码片段 (参考DuckX文档)
    // #include <duckx/duckx.hpp>
    // duckx::Document doc("path/to/your/document.docx");
    // doc.open();
    // for (auto p : doc.paragraphs()) {
    //     for (auto r : p.runs()) {
    //         std::cout << r.get_text() << std::endl;
    //     }
    // }
    ```

3.  **占位符识别逻辑实现**：
    -   设计算法在遍历文本运行时查找符合 `{{placeholder_name}}` 模式的字符串。
    -   考虑占位符可能跨越多个文本运行（runs）的情况。
    -   记录识别到的占位符名称及其在文档中的位置（例如，段落索引、运行索引）。

4.  **文本替换与格式保留测试**：
    -   **核心挑战点**：DuckX是否提供直接修改文本运行内容并保留其格式的API？
    -   **测试1（简单替换）**：尝试修改一个简单文本运行的内容。
        -   `r.set_text("new_value");` (假设API存在)
    -   **测试2（格式保留）**：
        -   如果占位符 `{{formatted_placeholder}}` 本身是加粗的，替换后的文本是否依然加粗？
        -   需要研究DuckX如何处理文本运行的属性（`rPr`）。如果DuckX允许访问和修改这些属性，或者在设置文本时默认保留，则格式保留有望实现。
        -   如果DuckX不直接支持保留格式的替换，需要研究是否可以通过更底层的XML操作（如果DuckX暴露了相关接口）或组合操作来实现。

5.  **文档保存与验证**：
    -   使用DuckX API保存修改后的文档到新文件。
    -   手动打开生成的文件，与原始模板和预期结果进行对比，重点检查：
        -   占位符是否被正确替换。
        -   格式是否按预期保留。
        -   文档其他部分是否完好无损。

6.  **局限性与问题记录**：
    -   详细记录在上述步骤中遇到的任何问题、DuckX库的限制（如不支持的Word特性、格式保留的困难等）。
    -   评估DuckX在处理复杂格式（如表格内格式、列表、图文混排）时的能力。

## 3. 预期产出

-   一个C++原型项目，演示使用DuckX进行 `.docx` 文件读取、占位符识别和文本替换的基本功能。
-   一份预研报告（可更新本文档），总结以下内容：
    -   DuckX的优缺点。
    -   在格式保留方面的能力和挑战。
    -   对于项目需求的满足程度评估。
    -   后续技术选型的建议（例如，是否继续使用DuckX，或考虑Spire.Doc、COM Interop等其他方案）。

## 4. 时间计划（预估）

-   环境搭建与DuckX编译集成：1-2天
-   基本文档读取与占位符识别原型：2-3天
-   文本替换与格式保留测试：3-5天（此为核心难点，可能耗时较多）
-   文档整理与报告撰写：1-2天

**总计：约 7-12 工作日**

## 5. 风险与应对

-   **风险1**：DuckX库在格式保留方面能力有限或存在Bug。
    -   **应对**：深入研究其源代码或寻求社区支持。如果问题无法解决，及时调整方案，评估Spire.Doc等商业库或COM Interop。
-   **风险2**：OOXML格式过于复杂，DuckX的抽象层次不足以应对所有格式细节。
    -   **应对**：明确预研阶段的目标是验证核心可行性。对于过于复杂的边缘情况，记录下来作为后续详细设计需要解决的问题，或调整需求范围。
-   **风险3**：编译和集成第三方C++库可能遇到环境问题。
    -   **应对**：严格按照官方文档操作，寻求社区帮助，确保开发环境配置正确。

---
*此计划将根据预研过程中的实际发现进行动态调整。*